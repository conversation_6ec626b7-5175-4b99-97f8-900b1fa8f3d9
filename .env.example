WEB_CONCURRENCY=
PORT=8080
NODE_ENV=development

# Redis
REDIS_HOST=
REDIS_PORT=
REDIS_TLS=

# JWT
JWT_EXPIRES_IN=
JWT_SECRET=

# MongoDB
MONGO_URI=

# Pinata API
PINATA_GATEWAY=
PINATA_API_KEY=
PINATA_API_SECRET=
PINATA_JWT=

# Ethermail API
ETHERMAIL_API_Key=
ETHERMAIL_API_Secret=

# Security
BCRYPT_ROUNDS=
MAX_OTP_ATTEMPTS=
OTP_EXPIRY_MINUTES=

# Proton Mail
SMTP_USERNAME_KARA=
SMTP_TOKEN_KARA=

SMTP_USERNAME_PARTNERS=
SMTP_TOKEN_PARTNERS=

SMTP_USERNAME_GUIDES=
SMTP_TOKEN_GUIDES=

SMTP_USERNAME_SCHOLARS=
SMTP_TOKEN_SCHOLARS=

SMTP_SERVER=smtp.protonmail.ch
SMTP_PORT=587