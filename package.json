{"name": "si3-ts-backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"dev": "nodemon --watch . --ext ts --exec \"ts-node server.ts\"", "build": "tsc", "start": "node dist/server.js", "vercel-build": "npm run build"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/helmet": "^0.0.48", "@types/hpp": "^0.2.6", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "@types/nodemailer": "^6.4.17", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@types/node-cron": "^3.0.11", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.0.1", "ethers": "^5.7.2", "express": "^5.1.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "node-cron": "^4.2.1", "nodemailer": "^7.0.4", "path-to-regexp": "^8.2.0", "xss-clean": "^0.1.4"}}