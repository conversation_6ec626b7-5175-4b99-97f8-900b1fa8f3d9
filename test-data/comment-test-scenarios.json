{
  "testScenarios": {
    "authentication": {
      "validLogin": {
        "email": "<EMAIL>",
        "password": "password123"
      },
      "scholarLogin": {
        "email": "<EMAIL>", 
        "password": "password123"
      },
      "adminLogin": {
        "email": "<EMAIL>",
        "password": "password123"
      }
    },
    
    "contentIds": {
      "guideSession": "guide-session-001",
      "guideIdeasLab": "guide-ideas-lab-001", 
      "scholarSession": "scholar-session-001",
      "scholarIdeasLab": "scholar-ideas-lab-001"
    },

    "createComments": {
      "validTopLevelComment": {
        "contentId": "guide-session-001",
        "contentType": "guide_session",
        "content": "This is a comprehensive comment about the guide session. It provides valuable insights and feedback."
      },
      
      "validReplyComment": {
        "contentId": "guide-session-001", 
        "contentType": "guide_session",
        "content": "This is a thoughtful reply to the previous comment. I agree with the points made.",
        "parentCommentId": "REPLACE_WITH_PARENT_COMMENT_ID"
      },
      
      "longComment": {
        "contentId": "guide-session-001",
        "contentType": "guide_session", 
        "content": "This is a very long comment that tests the character limit validation. ".repeat(50)
      },
      
      "minimumComment": {
        "contentId": "guide-session-001",
        "contentType": "guide_session",
        "content": "Hi"
      },
      
      "scholarComment": {
        "contentId": "scholar-session-001",
        "contentType": "scholar_session",
        "content": "This comment is for scholar session content and should only be accessible to scholars and admins."
      }
    },

    "invalidComments": {
      "emptyContent": {
        "contentId": "guide-session-001",
        "contentType": "guide_session",
        "content": ""
      },
      
      "invalidContentType": {
        "contentId": "guide-session-001", 
        "contentType": "invalid_type",
        "content": "This should fail due to invalid content type."
      },
      
      "missingContentId": {
        "contentType": "guide_session",
        "content": "This should fail due to missing content ID."
      },
      
      "invalidParentId": {
        "contentId": "guide-session-001",
        "contentType": "guide_session", 
        "content": "This should fail due to invalid parent comment ID.",
        "parentCommentId": "invalid_mongo_id"
      },
      
      "tooLongContent": {
        "contentId": "guide-session-001",
        "contentType": "guide_session",
        "content": "x".repeat(2001)
      }
    },

    "updateComments": {
      "validUpdate": {
        "content": "This is an updated comment with new information and insights."
      },
      
      "emptyUpdate": {
        "content": ""
      },
      
      "tooLongUpdate": {
        "content": "x".repeat(2001)
      }
    },

    "reactions": {
      "like": {
        "type": "like"
      },
      
      "dislike": {
        "type": "dislike"
      },
      
      "invalidReaction": {
        "type": "love"
      }
    },

    "queryParameters": {
      "getCommentsByContent": {
        "basic": {
          "contentId": "guide-session-001",
          "contentType": "guide_session"
        },
        
        "withPagination": {
          "contentId": "guide-session-001", 
          "contentType": "guide_session",
          "page": "1",
          "limit": "10"
        },
        
        "withReplies": {
          "contentId": "guide-session-001",
          "contentType": "guide_session", 
          "includeReplies": "true"
        },
        
        "invalidContentType": {
          "contentId": "guide-session-001",
          "contentType": "invalid_type"
        },
        
        "missingRequired": {
          "contentId": "guide-session-001"
        }
      },
      
      "pagination": {
        "firstPage": {
          "page": "1",
          "limit": "5"
        },
        
        "secondPage": {
          "page": "2", 
          "limit": "5"
        },
        
        "invalidPage": {
          "page": "0",
          "limit": "5"
        },
        
        "invalidLimit": {
          "page": "1",
          "limit": "0"
        }
      }
    }
  },

  "testSequences": {
    "basicCommentFlow": [
      "1. Login as guide user",
      "2. Create top-level comment on guide_session content", 
      "3. Create reply to the comment",
      "4. Get comments for the content",
      "5. Add like reaction to comment",
      "6. Get reaction stats",
      "7. Update comment content",
      "8. Get updated comment",
      "9. Remove reaction",
      "10. Delete comment"
    ],
    
    "permissionTesting": [
      "1. Login as guide user",
      "2. Try to comment on scholar_session content (should fail)",
      "3. Login as scholar user", 
      "4. Try to comment on guide_session content (should fail)",
      "5. Comment on scholar_session content (should succeed)",
      "6. Login as admin user",
      "7. Comment on both guide and scholar content (should succeed)"
    ],
    
    "threadingTest": [
      "1. Create parent comment",
      "2. Create multiple replies to parent",
      "3. Create replies to replies (nested threading)",
      "4. Get threaded comments",
      "5. Get specific comment replies",
      "6. Test pagination on replies"
    ],
    
    "reactionTesting": [
      "1. Create comment",
      "2. Add like reaction",
      "3. Try to add another like (should update existing)",
      "4. Change to dislike reaction", 
      "5. Remove reaction",
      "6. Get reaction stats throughout process"
    ]
  },

  "expectedResponses": {
    "successfulComment": {
      "status": "success",
      "data": {
        "comment": {
          "_id": "string",
          "contentId": "string", 
          "contentType": "string",
          "content": "string",
          "userId": "string",
          "parentCommentId": "string|null",
          "isReply": "boolean",
          "replyCount": "number",
          "reactions": "array",
          "likeCount": "number", 
          "dislikeCount": "number",
          "isEdited": "boolean",
          "isDeleted": "boolean",
          "createdAt": "string",
          "updatedAt": "string",
          "user": {
            "_id": "string",
            "email": "string",
            "roles": "array"
          }
        }
      }
    },
    
    "paginatedComments": {
      "status": "success",
      "results": "number",
      "pagination": {
        "page": "number",
        "limit": "number", 
        "totalPages": "number",
        "totalComments": "number",
        "hasNextPage": "boolean",
        "hasPrevPage": "boolean"
      },
      "data": {
        "comments": "array"
      }
    },
    
    "validationError": {
      "status": "error",
      "message": "Validation failed",
      "errors": "array"
    },
    
    "unauthorizedError": {
      "status": "error", 
      "message": "You are not authorized to access this resource"
    },
    
    "notFoundError": {
      "status": "error",
      "message": "Comment not found"
    }
  }
}
