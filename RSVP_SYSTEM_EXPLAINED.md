# RSVP System - How It Works

## System Overview

The RSVP system allows users to respond to events with Yes/No/Maybe responses, manages notifications, and provides role-based access control. Here's how everything works together.

## 🏗️ Architecture Flow

```
Sanity CMS → Event Cache → RSVP System → Notifications → Users
     ↓           ↓            ↓             ↓           ↓
  Events    Local Cache   User RSVPs   Email/In-App  Frontend
```

## 📊 Data Models

### 1. Event Cache (`EventCacheModel`)
Stores event data locally for fast access:
```javascript
{
  eventId: "event-123",           // Sanity document ID
  title: "Guide Session #1",
  description: "Weekly guide session",
  eventType: "GUIDE_SESSION",     // Determines who can access
  startDate: "2024-02-01T18:00:00.000Z",
  endDate: "2024-02-01T20:00:00.000Z",
  location: "Conference Room A",
  maxAttendees: 30,               // Optional capacity limit
  isActive: true,                 // Can users RSVP?
  sanityData: { /* raw Sanity data */ },
  lastSyncedAt: "2024-01-15T10:30:00.000Z"
}
```

### 2. RSVP (`RSVPModel`)
User responses to events:
```javascript
{
  eventId: "event-123",
  userId: ObjectId("user-456"),
  status: "YES" | "NO" | "MAYBE",
  notificationPreferences: {
    email: true,                  // Send email reminders?
    inApp: true,                  // Send in-app notifications?
    daysBefore: [7, 1]           // When to send (days before event)
  },
  notes: "Looking forward to it!",
  createdAt: "2024-01-15T10:30:00.000Z",
  updatedAt: "2024-01-15T10:35:00.000Z"
}
```

### 3. Notification Queue (`NotificationQueueModel`)
Scheduled notifications:
```javascript
{
  rsvpId: ObjectId("rsvp-789"),
  eventId: "event-123",
  userId: ObjectId("user-456"),
  notificationType: "email" | "inApp",
  scheduledFor: "2024-01-25T09:00:00.000Z",  // When to send
  status: "pending" | "sent" | "failed",
  attempts: 0,
  errorMessage: null
}
```

## 🔄 Complete RSVP Flow

### Step 1: Event Creation/Sync
```
1. Event created in Sanity CMS
2. Webhook triggers (or manual sync via API)
3. Event data validated and stored in EventCacheModel
4. Cache cleared for related data
```

### Step 2: User RSVP Process
```
1. User visits event page
2. Frontend checks user permissions (role-based access)
3. User submits RSVP form (YES/NO/MAYBE + preferences)
4. Backend validates:
   - Event exists and is active
   - Event hasn't started yet
   - Event isn't at capacity (for YES responses)
   - User has permission for this event type
5. RSVP created/updated in database
6. Notifications scheduled based on preferences
7. Cache cleared for affected data
8. Response sent to frontend
```

### Step 3: Notification Scheduling
```
When RSVP is created/updated:
1. Delete existing notifications for this RSVP
2. For each day in daysBefore array [7, 1]:
   - Calculate notification date (eventDate - days)
   - Skip if date is in the past
   - Create notification queue entries for email/inApp
3. Notifications wait in queue for processing
```

### Step 4: Notification Processing (Cron Job)
```
Every 5 minutes:
1. Find notifications where scheduledFor <= now AND status = 'pending'
2. For each notification:
   - Get RSVP and event data
   - Generate email template with event details
   - Send via ProtonMail
   - Mark as 'sent' or 'failed'
   - Log results
```

## 🔐 Role-Based Access Control

### Event Types & Access:
```javascript
const EVENT_ACCESS_MAP = {
  GUIDE_SESSION: ['GUIDE', 'ADMIN'],
  GUIDE_IDEAS_LAB: ['GUIDE', 'ADMIN'],
  SCHOLAR_SESSION: ['SCHOLAR', 'ADMIN'],
  SCHOLAR_IDEAS_LAB: ['SCHOLAR', 'ADMIN'],
  GENERAL_EVENT: ['GUIDE', 'SCHOLAR', 'ADMIN', 'PARTNER']
}
```

### Permission Checks:
1. **Authentication**: All RSVP routes require valid JWT token
2. **Event Access**: User role must match event type requirements
3. **RSVP Ownership**: Users can only view/modify their own RSVPs (except admins)
4. **Admin Privileges**: Admins can view all RSVPs and sync events

## 📧 Notification System Details

### Email Template Generation:
```javascript
// Automatic template with:
- Event title and details
- User's RSVP status badge
- Days until event countdown
- Professional HTML styling
- Responsive design
```

### Notification Preferences:
```javascript
// Default preferences
{
  email: true,        // Most users want email reminders
  inApp: true,        // In-app notifications (placeholder)
  daysBefore: [7, 1]  // Remind 1 week and 1 day before
}

// Common patterns:
[14, 7, 1]    // 2 weeks, 1 week, 1 day
[3, 1]        // 3 days, 1 day
[1]           // Just 1 day before
```

### Retry Logic:
- Failed notifications are marked with error message
- Cron job logs failures for monitoring
- No automatic retry (to prevent spam)
- Manual retry possible via admin tools

## 🚀 API Endpoints Workflow

### 1. Create/Update RSVP
```
POST /api/rsvp
→ Validate event exists and is accessible
→ Check capacity limits
→ Create/update RSVP record
→ Schedule notifications
→ Clear caches
→ Return success response
```

### 2. Get User's RSVPs
```
GET /api/rsvp/my-rsvps?page=1&limit=20&upcoming=true
→ Check authentication
→ Query user's RSVPs with pagination
→ Populate event details
→ Cache results
→ Return paginated response
```

### 3. Get Event RSVPs (Admin/Event Access)
```
GET /api/rsvp/event/EVENT_ID?status=YES&includeUser=true
→ Check event access permissions
→ Query RSVPs for specific event
→ Filter by status if provided
→ Include user details if requested
→ Cache results
→ Return paginated response
```

### 4. Event Statistics
```
GET /api/rsvp/event/EVENT_ID/stats
→ Check event access permissions
→ Calculate RSVP counts by status
→ Check capacity information
→ Cache results
→ Return statistics
```

## 🔧 Background Services

### Cron Service Jobs:
1. **Notification Processor** (every 5 minutes):
   - Processes pending notifications
   - Sends emails via ProtonMail
   - Updates notification status
   - Logs statistics

2. **Event Cleanup** (daily):
   - Removes expired events from cache
   - Cleans up old notification records
   - Maintains database performance

3. **Health Check** (hourly):
   - Monitors notification queue health
   - Alerts on high failure rates
   - Logs system metrics

## 📱 Frontend Integration Points

### 1. Event Display:
```javascript
// Check if user can access event
const canAccess = userRoles.some(role => 
  EVENT_ACCESS_MAP[event.eventType].includes(role)
);

// Get user's current RSVP
const userRSVP = await fetch(`/api/rsvp/event/${eventId}/my-rsvp`);
```

### 2. RSVP Form:
```javascript
// Submit RSVP with preferences
const rsvpData = {
  eventId: "event-123",
  status: "YES",
  notes: "Looking forward to it!",
  notificationPreferences: {
    email: true,
    inApp: true,
    daysBefore: [7, 1]
  }
};

await fetch('/api/rsvp', {
  method: 'POST',
  body: JSON.stringify(rsvpData)
});
```

### 3. Event Statistics (Admin):
```javascript
// Display RSVP counts and capacity
const stats = await fetch(`/api/rsvp/event/${eventId}/stats`);
// Shows: totalRSVPs, yesCount, noCount, maybeCount, capacity info
```

## 🔍 Monitoring & Debugging

### Log Messages to Watch:
```
✅ RSVP Cron jobs initialized
🔔 Processing pending notifications...
📊 Notification stats: X pending, Y sent, Z failed
💓 RSVP System Health Check
⚠️ X overdue notifications found
🚨 High failure rate: X failed notifications
```

### Common Issues:
1. **Notifications not sending**: Check ProtonMail credentials
2. **Events not syncing**: Verify Sanity webhook configuration
3. **Permission errors**: Check user roles and event types
4. **Capacity issues**: Verify maxAttendees settings

## 🎯 Key Features

### ✅ What Works:
- Role-based event access
- Automatic notification scheduling
- Email reminders with HTML templates
- Event capacity management
- RSVP statistics and reporting
- Cache optimization for performance
- Comprehensive error handling

### 🚧 Future Enhancements:
- In-app notifications (currently placeholder)
- WebSocket real-time updates
- Bulk RSVP operations
- Event waitlists
- Calendar integration
- SMS notifications

This system provides a complete event RSVP solution with automated notifications, role-based access, and comprehensive management features.
